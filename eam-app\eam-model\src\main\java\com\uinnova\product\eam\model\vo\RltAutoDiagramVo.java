package com.uinnova.product.eam.model.vo;

import com.binary.framework.bean.annotation.Comment;
import lombok.Data;

/**
 * description
 *
 * <AUTHOR>
 * @since 2025/6/12 16:17
 */
@Data
public class RltAutoDiagramVo {

    @Comment("起点ciCode")
    private String ciCode;
    @Comment("向上层数")
    private Integer up;
    @Comment("向下层数")
    private Integer down;
    @Comment("制品id")
    private Long artifactId;
    @Comment("是否检出至本地数据")
    private boolean checkOutLocal = false;
}
