package com.uinnova.product.eam.service;

import com.uinnova.product.eam.model.VcCiRltInfo;
import com.uinnova.product.eam.model.vo.*;
import com.uino.bean.cmdb.base.ESCIRltInfo;

import java.util.List;
import java.util.Set;

public interface CIRltExtendSvc {

    /**
     * 关系删除列表
     * @param ciRltDropCompareVo
     * @return
     */
    CIRltDropCompare delList(CIRltDropCompareVo ciRltDropCompareVo);

    /**
     * 关系被引用的资产列表
     * @param delParam
     * @return
     */
    List<CIRltQuotedAssert> quotedAsserts(CIRltDelParam delParam);

    /**
     * 关系删除-静默（视图发布）
     * @param privateDiagramIds
     */
    void rltDelCaseDiagramPush(Set<String> privateDiagramIds);

    /**
     * 关系删除-静默（资产库视图删除）
     * @param releaseDiagramIds
     */
    void rltDelCaseDesignDiagramDel(Set<String> releaseDiagramIds);

    /**
     * 关系删除
     * @param delParam
     * @param needApprove
     */
    void rltDelMerge(CIRltDelParam delParam, Boolean needApprove);

    /**
     * 检查三个库（私有库、设计库、基线库）中是否存在指定关系分类的关系数据
     * @param rltClassId 关系分类ID
     * @return true-存在关系数据，false-不存在关系数据
     */
    boolean exitRltData(Long rltClassId);

    List<VcCiRltInfo> getAutoDiagramRlt(RltAutoDiagramVo rltAutoDiagramVo);

    /**
     * 根据设计库的关系信息，在当前用户本地生成ci和关系数据
     * @param rltInfos
     * @return
     */
    List<ESCIRltInfo>  checkOutCiAndRltToUserLocal(List<VcCiRltInfo> rltInfos);

}
