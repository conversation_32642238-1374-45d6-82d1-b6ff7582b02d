<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="49e07847-65d7-4716-b901-3786eaf21094" name="更改" comment="fix：修复自定义response获取内容失败bug" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="develop-v6.5.0-dm-bes" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2xcG4DFUmZ3aOqc5lHN2gw4whQv" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.eam-base [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.eam-base [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.eam-base [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.eam-base [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.uino-eam-eventdrive [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.uino-eam-eventdrive [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.uino-eam-micro-service [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;develop-v6.5.0-jdk17-spring3.4.5&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/workspace/mycode/ea/uino-micro-base-ea&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;http.proxy&quot;,
    &quot;应用程序.RunLocal.executor&quot;: &quot;Debug&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager">
    <configuration name="RunLocal" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.uino.run.RunLocal" />
      <module name="uino-eam-micro-web" />
      <shortenClasspath name="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.RunLocal" />
        <item itemvalue="应用程序.RunLocal" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="49e07847-65d7-4716-b901-3786eaf21094" name="更改" comment="" />
      <created>1748229755439</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748229755439</updated>
      <workItem from="1748229756658" duration="151000" />
      <workItem from="1748229939424" duration="2649000" />
      <workItem from="1748252017079" duration="17355000" />
      <workItem from="1748332663537" duration="2463000" />
      <workItem from="1748426386310" duration="1050000" />
      <workItem from="1748427461289" duration="2324000" />
      <workItem from="1748483265567" duration="1741000" />
      <workItem from="1748485130340" duration="17278000" />
      <workItem from="1748915074511" duration="684000" />
      <workItem from="1748934542738" duration="1014000" />
      <workItem from="1748935587011" duration="805000" />
      <workItem from="1748936725068" duration="1444000" />
      <workItem from="1748939798591" duration="795000" />
      <workItem from="1748944482180" duration="191000" />
      <workItem from="1748945992353" duration="12640000" />
      <workItem from="1749103817868" duration="666000" />
      <workItem from="1749189935721" duration="1271000" />
      <workItem from="1749436000028" duration="5579000" />
      <workItem from="1749635720297" duration="12460000" />
    </task>
    <task id="LOCAL-00001" summary="fix：修复license定时任务注册报错bug">
      <option name="closed" value="true" />
      <created>1748257041428</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1748257041428</updated>
    </task>
    <task id="LOCAL-00002" summary="fix：修复license失效未跳转bug">
      <option name="closed" value="true" />
      <created>1748326249934</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1748326249934</updated>
    </task>
    <task id="LOCAL-00003" summary="refactor：优化初始化索引代码，使用推荐的写法重构">
      <option name="closed" value="true" />
      <created>1748949076268</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1748949076268</updated>
    </task>
    <task id="LOCAL-00004" summary="refactor：优化索引分片和index.mapping.total_fields.limit大小，将分类和关系相关索引设置为20000，并解决重启后索引重置为2000的bug">
      <option name="closed" value="true" />
      <created>1749017306279</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1749017306279</updated>
    </task>
    <task id="LOCAL-00005" summary="fix：修复自定义response获取内容失败bug">
      <option name="closed" value="true" />
      <created>1749637461812</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1749637461812</updated>
    </task>
    <option name="localTasksCounter" value="6" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="3bca614f-100e-4e69-967d-261669544499" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="3bca614f-100e-4e69-967d-261669544499">
          <value>
            <State>
              <option name="CUSTOM_BOOLEAN_PROPERTIES">
                <map>
                  <entry key="Show.Git.Branches" value="true" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="develop-v6.5.0-jdk17-spring3.4.5" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="develop-v6.5.0-jdk17-spring3.4.5" />
                      </list>
                    </value>
                  </entry>
                  <entry key="user">
                    <value>
                      <list>
                        <option value="*" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <option name="LOCAL_CHANGES_DETAILS_PREVIEW_SHOWN" value="false" />
    <MESSAGE value="fix：修复license定时任务注册报错bug" />
    <MESSAGE value="fix：修复license失效未跳转bug" />
    <MESSAGE value="es17" />
    <MESSAGE value="refactor：优化初始化索引代码，使用推荐的写法重构" />
    <MESSAGE value="refactor：优化索引分片和index.mapping.total_fields.limit大小，将分类和关系相关索引设置为20000，并解决重启后索引重置为2000的bug" />
    <MESSAGE value="fix：修复自定义response获取内容失败bug" />
    <MESSAGE value="暂存分割索引抽象类" />
    <option name="LAST_COMMIT_MESSAGE" value="暂存分割索引抽象类" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/uino-micro-plugin/src/main/java/com/uinnova/product/vmdb/comm/license/LicenseAuthorityFilter.java</url>
          <line>138</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>