package com.uino.dao.event;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.uino.bean.ap.param.EventStatisticsParam;
import com.uino.bean.monitor.base.ESMonEapEvent;
import com.uino.dao.AbstractSplitBaseDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.xcontent.XContentBuilder;
import org.elasticsearch.xcontent.XContentFactory;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;

import static com.uino.bean.event.FMEventConstants.FIRSTOCCURRENCE;
import static com.uino.dao.ESConst.INDEX_EP_EVENT_HISTORY_PREFIX;

/**
 * 告警按日期拆索引存入ES的Dao
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Repository
public class EventHistoryDao extends AbstractSplitBaseDao {

    // 告警统计分组显示数量
    @Value("${uino.monitor.event.requiredSize:1000}")
    private int requiredSize;

    @Override
    public String getIndexPrefix() {
        return INDEX_EP_EVENT_HISTORY_PREFIX;
    }

    @Override
    public String getType() {
        return "event";
    }

    public boolean saveOrUpdateBatchNoRefresh(JSONArray list) {
        // 循环每条数据，根据时间来分索引
        Map<String, JSONArray> saveMap = new HashMap<>();
        for (Object object : list) {
            JSONObject jsonObj = (JSONObject) object;
            Date date = jsonObj.getDate("firstOccurrence");
            String index = getIndexByTime(date.getTime());
            JSONArray jsonArray = saveMap.get(index);
            if (jsonArray == null) {
                jsonArray = new JSONArray();
                saveMap.put(index, jsonArray);
            }
            jsonArray.add(jsonObj);
        }
        for (Entry<String, JSONArray> entry : saveMap.entrySet()) {
            boolean saveOrUpdateBatchNoRefresh = saveOrUpdateBatchNoRefresh(entry.getValue(), entry.getKey());
            if (!saveOrUpdateBatchNoRefresh) {
                return false;
            }
        }
        return true;
    }

    /**
     * 保存一条信息
     * @param jsonObject
     * @return
     */
    public boolean saveOrUpdateNoRefresh(JSONObject jsonObject) {
        // 循环每条数据，根据时间来分索引
        Date date = jsonObject.getDate("firstOccurrence");
        boolean saveOrUpdateBatchNoRefresh = super.saveOrUpdateNoRefresh(jsonObject, date.getTime());
        if (!saveOrUpdateBatchNoRefresh) {
            return false;
        }
        return true;
    }

    public boolean saveOrUpdate(JSONObject jsonObject) {
        // 循环每条数据，根据时间来分索引
        Date date = jsonObject.getDate("firstOccurrence");
        boolean saveOrUpdate = super.saveOrUpdate(jsonObject, initIndexByTime(date.getTime()));
        if (!saveOrUpdate) {
            return false;
        }
        return true;
    }

    public boolean updateByQuery(ESMonEapEvent event, QueryBuilder query, String scriptStr) {
        log.info("updateByQuery FIRSTOCCURRENCE======" + event.get(FIRSTOCCURRENCE));
        String firstOccurrence = event.get(FIRSTOCCURRENCE);
        if (NumberUtils.isCreatable(firstOccurrence)) {
            String index = getIndexByTime(Long.parseLong(firstOccurrence));
            return updateByQuery(query, scriptStr, index);
        } else {
            return updateByQuery(query, scriptStr);
        }
    }

//    @Override
//    protected void initIndex(String index) {
//        try {
//            RestHighLevelClient restHighLevelClient = getClient();
//            GetIndexRequest getCi = new GetIndexRequest();
//            getCi.indices(index);
//            boolean existCi = restHighLevelClient.indices().exists(getCi, RequestOptions.DEFAULT);
//            if (!existCi) {
//                CreateIndexRequest createCIIndex = new CreateIndexRequest(index);
//                createCIIndex.settings(getSetting("my-analyzer"));
//                createCIIndex.mapping(index, getSelfMapping("my-analyzer", index));
//                restHighLevelClient.indices().create(createCIIndex, RequestOptions.DEFAULT);
//                HashMap<String, Object> map = new HashMap<>(2);
//                map.put("index.mapping.total_fields.limit", 2000);
//                map.put("index.max_result_window", 100000000);
//                UpdateSettingsRequest updateSettingsRequest = new UpdateSettingsRequest(index);
//                updateSettingsRequest.settings(map);
//                restHighLevelClient.indices().putSettings(updateSettingsRequest, RequestOptions.DEFAULT);
//            }
//        } catch (Exception e) {
//            log.error("init index mapping config error :: index== [" + index + "] :: type== [" + index + "]  " + e.getMessage(), e);
//        }
//    }

    @Override
    protected XContentBuilder getSelfMapping(String index) {
        XContentBuilder mapping = null;
        try {
            mapping = XContentFactory.jsonBuilder();
            mapping.startObject()
                    .startObject(index)
                    .startObject("properties")
                    .startObject("time").field("type", "date").endObject()
                    .startObject("value").field("type", "double").endObject()
                    .endObject()
                    .endObject()
                    .endObject();
        } catch (Exception e) {
            log.error("Occurred an exception during get mapping", e);
        }
        return mapping;
    }

    protected XContentBuilder getSetting(String analyzerName) {
        XContentBuilder setting = null;
        try {
            setting = XContentFactory.jsonBuilder();
            setting.startObject()
                        .startObject("analysis")
                            .startObject("analyzer")
                                .startObject(analyzerName)
                                .array("filter", "lowercase", "reverse")
                                .field("tokenizer", "ngram")
                                .field("min_gram", "1")
                                .field("max_gram", "1")
                                .endObject()
                            .endObject()
                        .endObject()
                    .endObject();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return setting;
    }

    /**
     * 按照字段统计记录数
     *
     * @param queryBuilder 查询参数
     * @param param        统计参数
     * @return
     */
    public Map<String, Long> getTermsFieldCount(QueryBuilder queryBuilder, EventStatisticsParam param) {
        Map<String, Long> map = new LinkedHashMap<>();
        try {
            TermsAggregationBuilder aggregation;
            // 统计维度字段为字符串类型，缺省值为"-"，数字类型缺省值为"0"；
            if (param.getAnalysisField().contains("keyword")) {
                aggregation = AggregationBuilders.terms("termsMap").field(param.getAnalysisField()).order(BucketOrder.count(false)).size(param.getBuilderSize()).missing("-").field(param.getAnalysisField()).shardSize(Integer.MAX_VALUE);

            } else {
                aggregation = AggregationBuilders.terms("termsMap").field(param.getAnalysisField()).order(BucketOrder.count(false)).size(param.getBuilderSize()).missing(0).field(param.getAnalysisField()).shardSize(Integer.MAX_VALUE);
            }
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(queryBuilder);
            sourceBuilder.aggregation(aggregation);
            sourceBuilder.size(param.getBuilderSize());
            SearchRequest searchRequest = new SearchRequest(getIndicesArrayByTime(param.getStartTime(), param.getEndTime()));
            searchRequest.source(sourceBuilder);
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            ParsedTerms parsedTerms = response.getAggregations().get("termsMap");
            parsedTerms.getBuckets().stream().forEach(e -> {
                String key = e.getKeyAsString();
                if (key == null || key.length() <= 0) {
                    key = "-";
                }
                map.put(key, e.getDocCount());
            });
        } catch (Exception e) {
            log.error("error: " + e);
            throw new RuntimeException(e);
        }
        return map;
    }

    /**
     * 按照字段分组统计记录数（分组字段分组统计）
     *
     * @param queryBuilder 查询参数
     * @param param        统计参数
     * @return
     */
    public Map<String, Map<String, Long>> getTermsFieldGroupCount(QueryBuilder queryBuilder, EventStatisticsParam param) {
        Map<String, Map<String, Long>> map = new LinkedHashMap();
        try {
            AggregationBuilder termsMap1;
            AggregationBuilder termsMap2;

            // 统计维度与分组字段为字符串类型，缺省值为"-"，数字类型缺省值为"0"；
            if (param.getGroupField().contains("keyword")) {
                termsMap1 = AggregationBuilders.terms("termsMap1").field(param.getGroupField()).size(requiredSize).missing("-").field(param.getGroupField()).shardSize(Integer.MAX_VALUE);
            } else {
                termsMap1 = AggregationBuilders.terms("termsMap1").field(param.getGroupField()).size(requiredSize).missing(0).field(param.getGroupField()).shardSize(Integer.MAX_VALUE);
            }

            if (param.getAnalysisField().contains("keyword")) {
                termsMap2 = AggregationBuilders.terms("termsMap2").field(param.getAnalysisField()).size(param.getBuilderSize()).order(BucketOrder.count(false)).missing("-").field(param.getAnalysisField()).shardSize(Integer.MAX_VALUE);
            } else {
                termsMap2 = AggregationBuilders.terms("termsMap2").field(param.getAnalysisField()).size(param.getBuilderSize()).order(BucketOrder.count(false)).missing(0).field(param.getAnalysisField()).shardSize(Integer.MAX_VALUE);
            }
            termsMap1.subAggregation(termsMap2);

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(queryBuilder);
            sourceBuilder.aggregation(termsMap1);
            sourceBuilder.size(param.getBuilderSize());
            SearchRequest searchRequest = new SearchRequest(getIndicesArrayByTime(param.getStartTime(), param.getEndTime()));
            searchRequest.source(sourceBuilder);
            SearchResponse response = getClient().search(searchRequest, RequestOptions.DEFAULT);
            ParsedTerms parsedTerms = response.getAggregations().get("termsMap1");
            parsedTerms.getBuckets().stream().forEach(e -> {
                ParsedTerms parsedTerms2 = e.getAggregations().get("termsMap2");
                Map<String, Long> map1 = new LinkedHashMap<>();
                parsedTerms2.getBuckets().forEach(map2 -> {
                    String key2 = map2.getKeyAsString();
                    if (key2 == null || key2.length() <= 0) {
                        key2 = "-";
                    }
                    map1.put(key2, map2.getDocCount());
                });
                String key = e.getKeyAsString();
                if (key == null || key.length() <= 0) {
                    key = "-";
                }
                map.put(key, map1);
            });
        } catch (Exception e) {
            log.error("error: " + e);
            throw new RuntimeException(e);
        }
        return map;
    }

    public int getRequiredSize() {
        return requiredSize;
    }

    public void setRequiredSize(int requiredSize) {
        this.requiredSize = requiredSize;
    }
}
